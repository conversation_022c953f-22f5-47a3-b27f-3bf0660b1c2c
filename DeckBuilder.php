<?php

declare(strict_types=1);

namespace tools\deckbuilder;

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/../deckanalyzer/vendor/autoload.php';

use tools\deckanalyzer\SynergyEvaluator;

class DeckBuilder
{
    private string $level;
    private object $statCards;
    private object $constants;
    private SynergyEvaluator $synergyEvaluator;

    public function __construct()
    {
        if (defined('API_PATH_ROOT')) {
            $this->statCards = json_decode(file_get_contents(API_PATH_ROOT . '/stats/cards.json'));
            $this->constants = json_decode(file_get_contents(__DIR__ . '/data/constants.json'));
        } else {
            throw new \Exception('API_PATH_ROOT is not defined');
        }

        $this->synergyEvaluator = new SynergyEvaluator();
    }

    /**
     * Creates a deck based on specified parameters.
     *
     * @param string $level The level of the deck to create (basic, intermediate, advanced).
     * @param array $params An associative array containing parameters for deck creation.
     * @return array An array containing the created deck, states, and alerts.
     */
    public function create(
        string $level,
        array $params = [
            "winConditionName" => null,
            "attackLevel" => "free",
            "championName" => null,
            "averageElixir" => null,
            "deckVersions" => 1,
            "evolutions" => 0
        ]
    ): array {
        if (!in_array($level, ['basic', 'intermediate', 'advanced']))
            throw new \Exception('Invalid level');

        if (!is_string($level))
            throw new \Exception('level must be a string');

        if (!isset($params['winConditionName']))
            $params['winConditionName'] = null;
        if (!isset($params['attackLevel']))
            $params['attackLevel'] = 'free';
        if (!isset($params['championName']))
            $params['championName'] = null;
        if (!isset($params['averageElixir']))
            $params['averageElixir'] = null;
        if (!isset($params['deckVersions']))
            $params['deckVersions'] = 1;
        if (!isset($params['evolutions']))
            $params['evolutions'] = 0;

        if (!is_string($params['winConditionName']) && !is_null($params['winConditionName']))
            throw new \Exception('winConditionName must be a string or null');
        if (!is_string($params['attackLevel']))
            throw new \Exception('attackLevel must be a string');
        if (!is_string($params['championName']) && !is_null($params['championName']))
            throw new \Exception('championName must be a string or null');
        if (!is_float($params['averageElixir']) && !is_null($params['averageElixir']))
            throw new \Exception('averageElixir must be a float or null');
        if (!is_int($params['deckVersions']))
            throw new \Exception('deckVersions must be an integer');
        if (!is_int($params['evolutions']))
            throw new \Exception('evolutions must be an integer');

        if (
            !is_null($params['winConditionName']) && !in_array($params['winConditionName'], array_column(array_filter($this->statCards->cards, function ($card) {
                return in_array('buildings', $card->targets);
            }), 'name'))
        )
            throw new \Exception('winConditionName "' . $params['winConditionName'] . '" is not a valid card name');
        if (
            !is_null($params['championName']) && !in_array($params['championName'], array_column(array_filter($this->statCards->cards, function ($card) {
                return $card->rarity == 'champion';
            }), 'name'))
        )
            throw new \Exception('championName "' . $params['championName'] . '" is not a valid card name');
        if (!in_array($params['attackLevel'], ['free', 'normal', 'aggressive', 'very-aggressive']))
            throw new \Exception('attackLevel is not a valid value');
        if (!is_null($params['averageElixir'])) {
            if ($params['averageElixir'] < 2.5 || $params['averageElixir'] > 5.0)
                throw new \Exception('averageElixir must be between 2.5 and 5.0');
        }
        if ($params['deckVersions'] < 1 || $params['deckVersions'] > 3)
            throw new \Exception('deckVersions must be between 1 and 3');
        if ($params['evolutions'] < 0 || $params['evolutions'] > 2)
            throw new \Exception('evolutions must be between 0 and 2');

        $this->level = $level;
        $resCrearMazo = [
            'decks' => [[], [], []],
            'states' => ['createdDeck1' => false, 'createdDeck2' => false, 'createdDeck3' => false],
            'alerts' => [],
            'log' => []
        ];
        $allCards = $this->groupCards($this->statCards);

        $param = $params;

        for ($i = 0; $i < $param['deckVersions']; $i++) { //sacar versiones del mazo
            ### Primera Version del mazo ###
            $iteracion = $this->level == 'basic' ? 1 : ($this->level == 'intermediate' ? 3 : 5);
            $ressetNewMazo = static::setNewMazo($allCards, $resCrearMazo, $param, $iteracion);
            $namesCardInMazo = $ressetNewMazo['namesCardInMazo'];
            $statsCardInMazo = $ressetNewMazo['statsCardInMazo'];
            array_push($resCrearMazo['log'], ['MazoV' . ($i + 1) . 'F1' => array_column($statsCardInMazo, 'cardSynMax')]); //log de la primera version del mazo

            ### integracion de evolucion ###
            if (isset($param['evolutions']) && $param['evolutions'] && count($namesCardInMazo) == 9 && !in_array(false, $namesCardInMazo)) {
                $CardSynMaxEvoInMazo = array_filter($statsCardInMazo, function ($a) { //filtra las cartas que fueron escogidas por la evolutions
                    return $a['evolution'] /* $a['cardSynMax']['isEvo'] */ ; //filtra todas las cartas
                });
                $sortCardSynMaxEvoInMazo = $CardSynMaxEvoInMazo;
                usort($sortCardSynMaxEvoInMazo, function ($a, $b) { //ordena las evos de mayor a menos sinergias
                    $aFormatted = number_format($a['cardSynMax']['synMax'], 2, '.', '');
                    $bFormatted = number_format($b['cardSynMax']['synMax'], 2, '.', '');
                    return bccomp($bFormatted, $aFormatted, 2); // Orden descendente
                });
                count($sortCardSynMaxEvoInMazo) < 2 && ($resCrearMazo['alerts'][] = '<span class="cs-color-IntenseOrange text-center">No se encontraron todas las evoluciones sinérgica</span>');
                $iterEvo = count($sortCardSynMaxEvoInMazo) < 3 ? count($sortCardSynMaxEvoInMazo) : $param['evolutions'];

                for ($i3 = 0; $i3 < $iterEvo; $i3++) { //pocisionar las evolutions al los slot correspondientes
                    unset($namesCardInMazo[array_search($sortCardSynMaxEvoInMazo[$i3]['name'], $namesCardInMazo)]);
                    array_unshift($namesCardInMazo, $sortCardSynMaxEvoInMazo[$i3]['name']);
                }
                array_push($resCrearMazo['log'], ['sortCardSynMaxEvoInMazo' => $sortCardSynMaxEvoInMazo]);
            }

            ####### Proceso final ########
            array_push($resCrearMazo['log'], ['MazoV' . ($i + 1) . 'F2' => array_column($statsCardInMazo, 'cardSynMax')]); //log de la segunda version del mazo
            $resCrearMazo['decks'][$i] = $namesCardInMazo;
            count($resCrearMazo['decks'][$i]) == 9 && !in_array(false, $resCrearMazo['decks'][$i]) ?
                ($resCrearMazo['states']['createdDeck' . ($i + 1)] = true) && array_push($resCrearMazo['alerts'], '<span class="cs-color-VibrantTurquoise text-center">Mazo #' . ($i + 1) . ' fue Creado Exitosamente</span><br>') :
                array_push($resCrearMazo['alerts'], '<span class="cs-color-IntenseOrange text-center">Mazo #' . ($i + 1) . ' No se ha podido Completar</span><br>');
        }

        return [
            'decks' => $resCrearMazo['decks'],
            'states' => $resCrearMazo['states'],
            'alerts' => $resCrearMazo['alerts'],
            'log' => $resCrearMazo['log']
        ];
    }

    /**
     * Groups cards into categories based on attack type and properties.
     *
     * @param object $allStatCards Object containing card data, typically from statCards.json.
     *                           Expected to have 'cards' (array of card objects) and 'towerCards' (array of tower card objects).
     * @return array An associative array where keys are categories ('win', 'ter', 'aer', 'dew', 'hech', 'tow')
     *               and values are arrays of card objects belonging to that category.
     */
    public function groupCards(object $allStatCards): array
    {
        $groupCardsArray = ["win" => [], "ter" => [], "aer" => [], "dew" => [], "hech" => [], "tow" => $allStatCards->towerCards];
        foreach ($allStatCards->cards as $card) { //obtener las cartas mayor a los datos especificado
            $dps = ($card->damage->level11 && $card->hitspeed) ? round($card->damage->level11 / $card->hitspeed) : 0;

            $card->type == 'spell' && $card->units == 0 && array_push($groupCardsArray['hech'], $card);
            in_array('buildings', $card->targets) && $card->type != 'building' && array_push($groupCardsArray['win'], $card); //cartas que atacan a estructuras - win condition
            in_array('air', $card->targets) && $card->type != 'building' && $card->type != 'spell' && array_push($groupCardsArray['aer'], $card);
            in_array('ground', $card->targets) && $card->type != 'building' && $card->type != 'spell' && array_push($groupCardsArray['ter'], $card);
            ($card->type == 'building' && $dps > 150) || ($card->type == 'troop' && $dps > 200) && array_push($groupCardsArray['dew'], $card);
        }

        return $groupCardsArray;
    }

    /**
     * Calculates synergy points for a card based on its attributes and the current deck.
     * @param array $deckInProgress The current deck of cards.
     * @param object $card The card for which to calculate synergy points.
     * @param string $groupCard The group of cards to which the card belongs.
     * @param array $params Additional parameters for the calculation.
     * @return array An associative array containing the calculated synergy points and related information.
     */
    public function getPointsCard(array $deckInProgress, object $card, string $groupCard, array $params): array
    {
        $deckInProgress = array_filter($deckInProgress, function ($a) use ($card): bool {
            return $a == false || (!$a && $a['name'] != $card->name);
        });

        $cardStats = [
            'name' => $card->name,
            'points' => 0.0,
            'isEvo' => false,
            'evoSyn' => 0,
            'msg' => [],
            'groupCard' => $groupCard
        ];

        $deckInProgressBasicStats = $this->deckArrayStatsBasic($deckInProgress);
        $arrspeedCom = ['null', 'slow', 'medium', 'fast', 'very fast', 'null'];
        $hitpoints = $params['evolutions'] ? ($card->statsEvo->hitpoints->level11 ?? 0) : ($card->hitpoints->level11 ?? 0);
        $damage = ($card->damage->level11 ?? 0) * ($card->units ?? 0);
        if (!$card->suicide) {
            $dps = ($card->damage->level11 && $card->hitspeed) ? round($card->damage->level11 / $card->hitspeed) : 0;
        } else {
            $dps = 0;
        }
        $fatalDamage = ($card->fatalDamage->level11 ?? 0) * ($card->units ?? 0);
        $towerDamage = ($card->towerDamage->level11 ?? 0) * ($card->units ?? 0);
        $cycles = $card->evolution ? ($card->statsEvo->cycles ?? 0) : 0;
        $totalDamage = $card->duration ? (($card->damage->level11 && $card->hitspeed) && ($card->damage->level11 ?? 0) / $card->hitspeed ?? 0) * ($card->units ?? 0) * $card->duration : 0;
        $deathSpellCard = false;

        if ($hitpoints <= 290)
            $deathSpellCard = 'hech1';
        elseif ($hitpoints < 408)
            $deathSpellCard = 'hech2';
        elseif ($hitpoints < 689)
            $deathSpellCard = 'hech3';
        elseif ($hitpoints < 985)
            $deathSpellCard = 'hech4';

        $cardByGroups = array_reduce($deckInProgress, function ($carry, $card) use ($params) {
            if ($card) {
                $carry[$params['groupCard']][] = $card;
            }
            return $carry;
        }, []);

        $structureInMazo = in_array('building', array_column($deckInProgress, 'type'));

        // Define attack level targets for precise validation
        $attackLevelTargets = [
            'normal' => [
                'dps' => 150,
                'hitpoints' => 1000,
                'range' => 4.5
            ],
            'aggressive' => [
                'dps' => 200,
                'hitpoints' => 1250,
                'range' => 6.0
            ],
            'very-aggressive' => [
                'dps' => 350,
                'hitpoints' => 1500,
                'range' => 7.0
            ]
        ];

        $cardStats['points'] += 0.25;

        // Validate attackLevel with direct points control
        if ($params['attackLevel'] != 'free' && isset($attackLevelTargets[$params['attackLevel']])) {
            $targets = $attackLevelTargets[$params['attackLevel']];
            $dpsDiff = abs($dps - $targets['dps']);
            $hpDiff = abs($hitpoints - $targets['hitpoints']);
            $cardRange = $card->range ?? 0;
            $rangeDiff = abs($cardRange - $targets['range']);

            // DPS validation with direct points
            if ($dpsDiff <= 25) {
                $cardStats['points'] += 2.0;
                array_push($cardStats['msg'], 'DPS excelente para nivel ' . $params['attackLevel'] . ': ' . $dps . ' (+2.0 puntos)');
            } elseif ($dpsDiff <= 50) {
                $cardStats['points'] += 1.5;
                array_push($cardStats['msg'], 'DPS muy bueno para nivel ' . $params['attackLevel'] . ': ' . $dps . ' (+1.5 puntos)');
            } elseif ($dpsDiff <= 75) {
                $cardStats['points'] += 1.0;
                array_push($cardStats['msg'], 'DPS bueno para nivel ' . $params['attackLevel'] . ': ' . $dps . ' (+1.0 puntos)');
            }

            // Hitpoints validation with direct points
            if ($hpDiff <= 150) {
                $cardStats['points'] += 1.0;
                array_push($cardStats['msg'], 'HP excelente para nivel ' . $params['attackLevel'] . ': ' . $hitpoints . ' (+1.0 puntos)');
            } elseif ($hpDiff <= 300) {
                $cardStats['points'] += 0.75;
                array_push($cardStats['msg'], 'HP muy bueno para nivel ' . $params['attackLevel'] . ': ' . $hitpoints . ' (+0.75 puntos)');
            } elseif ($hpDiff <= 500) {
                $cardStats['points'] += 0.5;
                array_push($cardStats['msg'], 'HP bueno para nivel ' . $params['attackLevel'] . ': ' . $hitpoints . ' (+0.5 puntos)');
            }

            // Range validation with direct points
            if ($rangeDiff <= 0.25) {
                $cardStats['points'] += 1.0;
                array_push($cardStats['msg'], 'Rango excelente para nivel ' . $params['attackLevel'] . ': ' . $cardRange . ' (+1.0 puntos)');
            } elseif ($rangeDiff <= 0.5) {
                $cardStats['points'] += 0.75;
                array_push($cardStats['msg'], 'Rango muy bueno para nivel ' . $params['attackLevel'] . ': ' . $cardRange . ' (+0.75 puntos)');
            } elseif ($rangeDiff <= 0.75) {
                $cardStats['points'] += 0.5;
                array_push($cardStats['msg'], 'Rango bueno para nivel ' . $params['attackLevel'] . ': ' . $cardRange . ' (+0.5 puntos)');
            }
        }

        // Validating winConditionName parameter
        !is_null($params['winConditionName']) && $params['winConditionName'] == $card->name && ($cardStats['points'] += $this->constants->SYNERGY_CALCULATION_WIN_CARD) && array_push($cardStats['msg'], '+25.0 sinergia de eleccion de win por el usuario');

        // Validating championName parameter
        !is_null($params['championName']) && $params['championName'] == $card->name && ($cardStats['points'] += $this->constants->SYNERGY_CALCULATION_CHAMPION_CARD) && array_push($cardStats['msg'], '+25.0 Sinergia de campeon elegida por el usuario');

        // Validating averageElixir parameter
        if ($groupCard != 'tow' && !is_null($params['averageElixir'])) {
            $diff = abs($card->elixirCost - $params['averageElixir']);

            if ($diff <= 0.5) { // Card elixir cost is very close to the target average
                $cardStats['points'] += 1.5; // High synergy
                array_push($cardStats['msg'], 'Sinergia con elixir promedio solicitado (muy cercano)');
            } elseif ($diff <= 1.0) { // Card elixir cost is within 1 elixir of target
                $cardStats['points'] += 1.0; // Moderate synergy
                array_push($cardStats['msg'], 'Sinergia con elixir promedio solicitado (cercano)');
            } elseif ($diff <= 1.5) { // Card elixir cost is within 1.5 elixir of target
                $cardStats['points'] += 0.5; // Low synergy
                array_push($cardStats['msg'], 'Sinergia con elixir promedio solicitado (algo cercano)');
            }
        }

        if ($this->level == 'advanced') {
            // prepare card for synergy evaluation
            $card->dps = (object) ['level11' => $dps];
            $card->groupCard = $groupCard;
            $card->specialKeys = (object) [];
            $card->specialEvoKeys = (object) [];
            $card->evolvedInDeck = $params['evolutions'] && (!$deckInProgress[0] || !$deckInProgress[1]) && $card->evolution;

            // points for synergy general
            $this->synergyEvaluator->setContext($card, null, (object) $deckInProgressBasicStats);
            foreach (['adaptability', 'defense'] as $strategyMapName) {
                $applicableRules = $this->synergyEvaluator->getApplicableSynergies(
                    $strategyMapName,
                    'General'
                );

                foreach ($applicableRules as $condition) {
                    if ($condition->condition) {
                        $cardStats['points'] += $condition->points * 0.5;
                        array_push($cardStats['msg'], $condition->message . ' (+' . ($condition->points * 0.5) . ' puntos)');
                    }
                }
            }

            // points for synergy with other cards in deck
            foreach ($deckInProgress as $cardInDeck) {
                if ($cardInDeck) {
                    $this->synergyEvaluator->setContext($cardInDeck, $card, (object) $deckInProgressBasicStats);
                    foreach (['adaptability', 'defense'] as $strategyMapName) {
                        $applicableRules = $this->synergyEvaluator->getApplicableSynergies(
                            $strategyMapName,
                            'Cards'
                        );

                        foreach ($applicableRules as $condition) {
                            if ($condition->condition) {
                                $cardStats['points'] += $condition->points * 0.25;
                                array_push($cardStats['msg'], $condition->message . ' (+' . ($condition->points * 0.25) . ' puntos)');
                            }
                        }
                    }
                }
            }
        }

        return $cardStats;
    }

    /**
     * Sets a new deck based on the provided parameters and filters.
     * @param array $allCards An array of all available cards.
     * @param array $resCrearMazo An array containing the current state of the deck creation process.
     * @param array $param An array of parameters for deck creation.
     * @param int $filtros The number of filters to apply.
     * @return array An array containing the updated state of the deck creation process.
     */
    public function setNewMazo(array $allCards, array &$resCrearMazo, array $param, int $filtros): array
    {
        $posCard = ['win', 'ter', 'ter', 'aer', 'aer', 'dew', 'hech', 'hech', 'tow']; //grupos de cartas en su posicion del mazo
        $namesCardInMazo = $statsCardInMazo = [false, false, false, false, false, false, false, false, false];
        $DeckVersions = [];

        for ($i = 0; $i < $filtros; $i++) { //refinar el mazo
            foreach ($allCards as $groupCard => $valuegroupCards) { //iterar en cada categoria aer, ter, etc...
                $getCardsMaxSinF0 = static::getCardsMaxSin(
                    $valuegroupCards,
                    array_merge($param, ['evolutions' => 0, 'numcards' => (($groupCard == 'ter' || $groupCard == 'aer' || $groupCard == 'hech') ? 2 : 1), 'index' => 0, 'groupCard' => $groupCard]),
                    ['namesCardInMazo' => $namesCardInMazo, 'statsCardInMazo' => $statsCardInMazo, 'decks' => array_merge($resCrearMazo['decks'][0], $resCrearMazo['decks'][1], $resCrearMazo['decks'][2])]
                ); //itera las cartas retornadas
                !empty($getCardsMaxSinF0['alerts']) && array_push($resCrearMazo['log'], $getCardsMaxSinF0['alerts']);
                foreach ($getCardsMaxSinF0['cards'] as $keycardSynMaxV1 => $cardSynMaxV1) { //itera las cartas retornadas
                    $namesCardInMazo[($keycardSynMaxV1 + array_search($groupCard, $posCard))] = $cardSynMaxV1['name'];
                    $statsCardInMazo[($keycardSynMaxV1 + array_search($groupCard, $posCard))] = array_merge(json_decode(json_encode($this->getCard($cardSynMaxV1['name'])), true), ['cardSynMax' => $cardSynMaxV1['cardSynMax']]); //añade la carta a un array para poder usar su inf en proximas elecciones de cards
                }
            }

            $statsMazo = $this->deckArrayStatsBasic(json_decode(json_encode($statsCardInMazo), true));
            array_push($DeckVersions, ['synMaxAll' => $statsMazo['synMaxAll'], 'namesCardInMazo' => $namesCardInMazo, 'statsCardInMazo' => $statsCardInMazo]);
        }

        usort($DeckVersions, function ($a, $b) {
            $aFormatted = number_format($a['synMaxAll'], 2, '.', '');
            $bFormatted = number_format($b['synMaxAll'], 2, '.', '');
            return bccomp($bFormatted, $aFormatted, 2); // Orden descendente
        });

        $resCrearMazo['log'][] = $DeckVersions;
        return $DeckVersions[0];
    }

    /**
     * Retrieves the maximum synergy cards based on the provided parameters and statistics.
     * @param array $cards An array of cards to evaluate.
     * @param array $param An array of parameters for card selection.
     * @param array $stats An array containing statistics for card selection.
     * @return array An array containing the maximum synergy cards and related information.
     */
    public function getCardsMaxSin(array $cards, array $param, array $stats): array
    {
        $resgetCardsMaxSin = ['cards' => [], 'alerts' => [], 'state' => 'inprogress'];

        $staMaz = $this->deckArrayStatsBasic(json_decode(json_encode($stats['statsCardInMazo']), true));

        for ($i2 = 0; $i2 < $param['numcards']; $i2++) {
            ($hechizos = ['hech2', 'hech4']) &&
                $staMaz['costElixTrops'] <= 26 && ($hechizos = ['hech2', 'hech3']) &&
                $staMaz['costElixTrops'] <= 23 && ($hechizos = ['hech1', 'hech3']) &&
                $staMaz['costElixTrops'] <= 20 && ($hechizos = ['hech1', 'hech2']); //establece los tipos de hechizos a escoger

            $cardSynMax = ['name' => null, 'synMax' => 0.0, 'isEvo' => false, 'costElixcard' => 0, 'dieHech' => null, 'msg' => [], 'dpsdamage' => 0, 'evoSyn' => 0];

            foreach ($cards as $card) {
                if (in_array($card->name, $stats['namesCardInMazo']))
                    continue;

                // verifica si esta en otro mazo ya creado como otra opcion (deckVersions)
                if (
                    in_array($card->name, $stats['decks']) &&
                    ((in_array('buildings', $card->targets) && $param['winConditionName'] == "null") ||
                        (in_array('ground', $card->targets) && $param['index'] == 0) ||
                        (in_array('air', $card->targets) && $param['index'] == 0))
                )
                    continue;

                $cardTypeSpell = $this->getSpellType($card);

                if (($param['groupCard'] == 'hech' && ($param['index'] == 0 && ($cardTypeSpell == $hechizos[0] || $cardTypeSpell == $hechizos[0])) || ($param['index'] == 1 && ($cardTypeSpell == $hechizos[1] || $cardTypeSpell == $hechizos[1]))) || $param['groupCard'] != 'hech') {
                    $cardsyn = $this->getPointsCard($stats['statsCardInMazo'], $card, $param['groupCard'], $param); //datos de la carta a probar si tiene suficientes sinergias
                    //$_POST['version'] == '1.1' && $cardsyn = $DeckAnalyzer->synCardv1_1($stats['statsCardInMazo'], $card, array_merge($param, ['hechizos' => $hechizos])); //datos de la carta a probar si tiene suficientes sinergias

                    //condiciones obligatorias para seleccionar la carta
                    if (
                        $cardsyn['points'] > $cardSynMax['synMax'] && // si es mayor que la ultima en sinergia
                        (!in_array($card->name, /* array_slice( */ $stats['namesCardInMazo']/* , 0, (array_search($card->name, $stats['namesCardInMazo']) + $param['index'])) */)) && //si la carta no esta en el mazo
                        (($card->rarity != 'champion') || ($card->rarity == 'champion' && !in_array('champion', array_column($stats['statsCardInMazo'], 'rarity')))) // si no hay otra champion en el mazo
                    ) {
                        $cardSynMax['name'] = $cardsyn['name'];
                        $cardSynMax['synMax'] = $cardsyn['points'];
                        $cardSynMax['isEvo'] = $cardsyn['isEvo'];
                        $cardSynMax['msg'] = $cardsyn['msg'];
                        $cardSynMax['evoSyn'] = $cardsyn['evoSyn'];
                    } else {
                        continue;
                    }
                }
            }

            if ($cardSynMax['name'] != null) { //asigna los nuevos valores a los array respectivos
                $statCardSynMaz = $this->getCard($cardSynMax['name']);
                array_push($stats['namesCardInMazo'], $cardSynMax['name']); //añade la carta al array del mazo
                array_push($stats['statsCardInMazo'], array_merge(json_decode(json_encode($statCardSynMaz), true), ['cardSynMax' => $cardSynMax])); //añade la carta al array para poder usar su inf en proximas elecciones de cards
                array_push($resgetCardsMaxSin['cards'], array_merge(json_decode(json_encode($statCardSynMaz), true), ['cardSynMax' => $cardSynMax]));
                // array_push($resgetCardsMaxSin['alerts'], $cardSynMax['name'] . " elegida en el grupo " . $param['groupCard'] . " ahora el array namesCardInMazo es: " . json_encode($stats['namesCardInMazo']));
            } else { // sin no se encontro ninguna carta con sinergia para este grupo
                array_push($resgetCardsMaxSin['alerts'], 'No se encontro ninguna Carta con sinergia del grupo ' . $param['groupCard']) . ' para Versatilidad';
                $resgetCardsMaxSin['state'] = 'error';
                break;
            }
            $param['index']++;
        }

        return $resgetCardsMaxSin;
    }

    private function getSpellType(object $card): string
    {
        if ($card->type != 'spell')
            return 'unknown';

        $typeHech = 'unknown';

        if (
            ($card->elixirCost == 2 && $card->damage->level11 <= 250) ||
            ($card->elixirCost == 3 && $card->damage->level11 <= 250)
        )
            $typeHech = 'hech1';

        if ($card->elixirCost == 3 && $card->damage->level11 > 250)
            $typeHech = 'hech2';

        $card->elixirCost == 4 && ($typeHech = 'hech3');
        $card->elixirCost == 6 && ($typeHech = 'hech4');
        $card->damage->level11 <= 100 && ($typeHech = 'hech0');

        return $typeHech;
    }

    private function getInitialDeck(): array
    {
        $cardPositions = ['win', 'ter', 'ter', 'aer', 'aer', 'dew', 'hech', 'hech', 'tow'];
        $namesCardInMazo = $statsCardInMazo = [false, false, false, false, false, false, false, false, false];
        $allCardsGroupCards = $this->groupCards($this->statCards);

        foreach ($cardPositions as $keyPosition => $position) {
            foreach ($allCardsGroupCards[$position] as $card) {
                if (!in_array($card->name, $namesCardInMazo)) {
                    $namesCardInMazo[$keyPosition] = $card->name;
                    $statsCardInMazo[$keyPosition] = $card;
                } else {
                    continue;
                }
                break;
            }
        }

        return ["names" => $namesCardInMazo, "stats" => $statsCardInMazo];
    }

    public function getCard($nameId)
    {
        $json = $this->statCards;
        foreach ($json->towerCards as $key => $value) {
            if ($value->name == $nameId || $value->id == $nameId) {
                return $value;
            }
        }
        foreach ($json->cards as $key => $value) {
            if ($value->name == $nameId || $value->id == $nameId) {
                return $value;
            }
        }
        return null;
    }

    public static function deckArrayStatsBasic(array $Mazo0): array
    {
        $Mazo = array_filter($Mazo0, fn($a) => $a); // Remove null/false entries

        $stats = [
            'elixirCostAll' => 0,
            'costElixTrops' => 0,
            'agresividad' => 0,
            'cardsSplash' => 0,
            'cardsUnique' => 0,
            'unitsAll' => 0,
            'synMaxAll' => 0.0,
            'spells' => []
        ];

        foreach ($Mazo as $card) {
            $stats['elixirCostAll'] += $card['elixirCost'];
            $stats['unitsAll'] += $card['units'];
            $stats['synMaxAll'] += $card['cardSynMax']['synMax'];

            if (($card['type'] ?? '') == 'spell') {
                $stats['spells'][] = $card;
                $stats['agresividad'] += $card['damage']['level11'];
            } else {
                // For non-spells
                if (($card['type'] ?? '') != 'tower') {
                    $stats['costElixTrops'] += $card['elixirCost'];
                    $stats['agresividad'] += ($card['damage']['level11'] && $card['hitspeed']) ? $card['damage']['level11'] / $card['hitspeed'] : 0; // Add troop/building DPS to aggressiveness

                    if (($card['typeAttack'] ?? 'unique') == 'splash' && ($card['type'] ?? '') != 'building') {
                        $stats['cardsSplash']++;
                    } else {
                        $stats['cardsUnique']++;
                    }
                }
            }
        }

        return $stats;
    }
}
