<?php

/**
 * PHP Stubs for DEVSENSE PHP extension
 * This file helps the IDE recognize classes from external projects
 */

namespace tools\deckanalyzer {

    /**
     * Stub for SynergyEvaluator class from deckanalyzer project
     */
    class SynergyEvaluator
    {
        public function __construct()
        {
        }

        public function setContext(object $card, object|null $cardInDeck, object $deckBasicStats): void
        {
        }

        public function getApplicableSynergies(string $strategyName, string $synergyType): array
        {
            return [];
        }
    }

    // Agrega aquí más clases de deckanalyzer que uses en tu proyecto
    // Por ejemplo:
    // class OtraClase
    // {
    //     public function __construct() {}
    //     // métodos públicos que uses
    // }
}
