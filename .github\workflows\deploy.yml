name: Deploy DeckBuilder via FTP

on:
  # Deploy after successful release
  workflow_run:
    workflows: ["Release"]
    types:
      - completed
    branches:
      - main
  # Allow manual deployment
  workflow_dispatch:

jobs:
  ftp-deploy:
    runs-on: ubuntu-latest
    # Only deploy if the release workflow succeeded or if manually triggered
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout del código
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Solo trae el último commit, evita traer .git/

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.2"
          extensions: mbstring, json
          tools: composer:v2

      - name: Verificar archivos requeridos
        run: |
          echo "Verificando archivos críticos..."
          if [ ! -f "DeckBuilder.php" ]; then
            echo "❌ Error: DeckBuilder.php no encontrado"
            exit 1
          fi
          if [ ! -f "composer.json" ]; then
            echo "❌ Error: composer.json no encontrado"
            exit 1
          fi
          echo "✅ Todos los archivos críticos están presentes"

      - name: Instalar dependencias de Composer
        run: |
          echo "🔧 Instalando dependencias de Composer..."
          composer install --no-dev --optimize-autoloader --no-interaction --prefer-dist
          echo "✅ Dependencias instaladas y autoloader optimizado"

      - name: Verificar autoloader
        run: |
          echo "🔍 Verificando autoloader generado..."
          if [ ! -f "vendor/autoload.php" ]; then
            echo "❌ Error: vendor/autoload.php no encontrado"
            exit 1
          fi
          if [ ! -f "vendor/composer/autoload_classmap.php" ]; then
            echo "❌ Error: autoload_classmap.php no encontrado"
            exit 1
          fi
          echo "✅ Autoloader verificado correctamente"

      - name: Mostrar información de despliegue
        run: |
          echo "🚀 Iniciando despliegue..."
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Workflow: ${{ github.workflow }}"

      - name: Desplegar por FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          server-dir: /public_html/api/tools/deckbuilder/
          local-dir: ./
          exclude: |
            .git/
            **/.git/**
            **/.gitignore
            **/.github/**
            **/node_modules/**
            **/tests/**
            **/.env*
            **/.releaserc*
            **/package-lock.json
            **/package.json
            **/.vscode/**
            **/.clinerules/**
            **/README*.md
            **/CHANGELOG.md
            **/*.log
            **/.DS_Store
            **/Thumbs.db
            **/.npmrc
            **/.editorconfig

      - name: Verificar despliegue
        run: |
          echo "✅ Despliegue completado exitosamente"
